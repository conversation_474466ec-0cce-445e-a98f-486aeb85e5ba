import { useState, useEffect, useMemo, useRef } from 'react';
import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import { Button, CustomCheckbox, InputField, Modal } from '@Components/UI';
import clsx from 'clsx';
import {
  ClockIcon,
  CloseIcon,
  DeleteIcon,
  DownloadIcon,
  Loader,
  PdfIcon,
} from '@Icons';
import dayjs from 'dayjs';
import isToday from 'dayjs/plugin/isToday';
import isYesterday from 'dayjs/plugin/isYesterday';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import localeData from 'dayjs/plugin/localeData';
import { useNavigate } from 'react-router';
import { PATHS } from '@Config/Path.Config';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import { useDebounce } from '@Hooks/useDebouce';
import { trim } from '@Helpers/Utils';

dayjs.extend(isToday);
dayjs.extend(isYesterday);
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(localeData);
dayjs.extend(isToday);
dayjs.extend(isYesterday);
dayjs.extend(localeData);

const formatDate = (dateString: string) => {
  const localDate = dayjs.utc(dateString).tz(dayjs.tz.guess()); // Convert to local timezone
  const formattedDate = localDate.format('dddd D MMMM YYYY'); // Example: "Friday 14 February 2025"

  if (localDate.isToday()) {
    return `Today - ${formattedDate}`;
  } else if (localDate.isYesterday()) {
    return `Yesterday - ${formattedDate}`;
  } else {
    return formattedDate; // Default format for other dates
  }
};

const formatTime = (dateString: string) => {
  const localDate = dayjs.utc(dateString).tz(dayjs.tz.guess()); // Convert to local timezone
  const formattedDate = localDate.format('HH:mm');
  return formattedDate;
};

// Define TypeScript types
interface Session {
  id: number;
  session_title: string;
  session_id: string;
  started_at: string;
  updated_at: string;
  ended_at: string | null;
  download_available?: boolean;
}

type GroupedSessions = {
  date: string;
  items: Session[];
};
interface TabDataListType {
  id: number;
  name: string;
}
const tabDataList: TabDataListType[] = [
  {
    id: 1,
    name: 'Recent Search',
  },
  {
    id: 2,
    name: 'Recent Calculations',
  },
];

// Function to group sessions by date
const groupByDate = (sessions: Session[]): GroupedSessions[] => {
  const grouped = sessions?.reduce<Record<string, Session[]>>(
    (acc, session) => {
      const date = session.updated_at.split(' ')[0]; // Extract date part
      if (!acc[date]) acc[date] = [];
      acc[date].push(session);
      return acc;
    },
    {}
  );

  return Object.entries(grouped)
    .sort(
      ([dateA], [dateB]) =>
        new Date(dateB).getTime() - new Date(dateA).getTime()
    )
    .map(([date, items]) => ({ date, items }));
};

const History = ({ itemsPerPage = 15 }: { itemsPerPage?: number }) => {
  const api = new Api();
  const navigate = useNavigate();
  const { addToast } = useToast();
  const observerRef = useRef<HTMLDivElement | null>(null);
  const latestEntryRef = useRef<HTMLDivElement | null>(null);

  const [sessions, setSessions] = useState<Session[]>([]);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [deleteSessionIds, setDeleteSessionIds] = useState<number[]>([]);
  const [isDeleteOpen, setIsDeleteOpen] = useState<boolean>(false);
  const [isDeleteLoading, setIsDeleteLoading] = useState<boolean>(false);

  const [search, setSearch] = useState<string>('');
  const debouncedSearchTerm = useDebounce(search, 1000);

  useEffect(() => {
    // If debouncedSearchTerm is active, the search effect is already handling the fetch.
    if (debouncedSearchTerm.trim() === '') {
      fetchSessions();
    }
  }, [currentPage, activeTab]);

  useEffect(() => {
    setCurrentPage(1);
    fetchSessions(true);
  }, [debouncedSearchTerm]);

  // Infinite Scroll Observer
  useEffect(() => {
    if (loading) return;

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setCurrentPage((p) => Math.min(p + 1, totalPages));
        }
      },
      { threshold: 1.0 }
    );

    if (observerRef.current) observer.observe(observerRef.current);

    return () => observer.disconnect();
  }, [loading]);

  const fetchSessions = async (reset: boolean = false) => {
    setLoading(true);
    try {
      const response = await api.get(
        activeTab === 1
          ? API_PATHS.CHAT_SESSION_HISTORY
          : API_PATHS.QUICK_CALC_SEARCH,
        {
          params: {
            page_size: itemsPerPage,
            page: reset ? 1 : currentPage,
            search: debouncedSearchTerm,
          },
        }
      );
      let tempSessions: Session[] = [];
      if (sessions?.length && !reset) {
        tempSessions = [...sessions];
      }
      tempSessions = [...tempSessions, ...response.data.data.list];
      setSessions([...tempSessions]);
      setTotalPages(response?.data?.data?.total_pages);
    } catch (err) {
      addToast('error', err as string);
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const groupedSessions = useMemo(() => groupByDate(sessions), [sessions]);

  // Slice paginated data based on grouped sessions
  const paginatedData = useMemo(() => {
    return groupedSessions.slice(0, currentPage * itemsPerPage);
  }, [groupedSessions, currentPage, itemsPerPage]);

  // Smooth Scroll to Latest Entry
  useEffect(() => {
    if (latestEntryRef.current && paginatedData?.length && currentPage > 1) {
      latestEntryRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  }, [paginatedData, currentPage]); // Triggers when new sessions are added

  const handleSelectSession = (id: number) => {
    setDeleteSessionIds((prev) =>
      prev.includes(id)
        ? prev.filter((sessionId) => sessionId !== id)
        : [...prev, id]
    );
  };

  const handleDeleteSessions = async () => {
    try {
      setIsDeleteLoading(true);
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const response = await api.post(
        activeTab === 2
          ? API_PATHS.DELETE_CALC_SESSION
          : API_PATHS.DELETE_CHAT_SESSION,
        {
          data: { ids: deleteSessionIds },
        }
      );
      setDeleteSessionIds([]);
      setSessions([]);
      setIsDeleteOpen(false);
      setCurrentPage(1);
      fetchSessions(true);
    } catch (err) {
      addToast('error', err as string);
    } finally {
      setIsDeleteLoading(false);
    }
  };

  const searchHandler = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchTerm = e.target.value.trim();

    if (!search.trim() && searchTerm === '') {
      return;
    }

    setLoading(true);
    setSessions([]);
    setSearch(searchTerm);
  };


  return (
    <div className="flex flex-col h-full w-full p-10 overflow-hidden">
      <Modal
        isOpen={isDeleteOpen}
        onClose={() => {
          setIsDeleteOpen(false);
        }}
        hideCloseButton
        children={
          <div className="flex flex-col items-center gap-[36px] pb-4 px-4">
            <div className="bg-[#FFD8E0] h-14 w-14 flex justify-center items-center rounded-full">
              <CloseIcon width={18} height={18} fill="#FF3B30" />
            </div>
            <div className="text-black font-bold text-center w-full">
              Are you sure want to delete selected session?
            </div>
            <div className=" w-full flex justify-center gap-6">
              <Button
                onClick={handleDeleteSessions}
                text="Yes"
                variant="other"
                className="bg-white shadow-primary border text-red-500 border-red-500 "
                loading={isDeleteLoading}
                fillLoader="#fb2c36"
              />
              <Button
                text="No"
                variant="outline"
                disabled={isDeleteLoading}
                onClick={() => {
                  setIsDeleteOpen(false);
                  setDeleteSessionIds([]);
                }}
              />
            </div>
          </div>
        }
      />
      <div>
        <InputField
          containerClassName={'pt-0'}
          placeholder="Search..."
          inputClassName="placeholder-primary-100"
          onChange={searchHandler}
          value={search}
        />
      </div>
      <div className="border border-l-0 border-r-0 border-t-0 border-b-[#F1F1F1] flex items-center justify-between pt-8">
        <div className=" flex gap-1">
          {tabDataList.map((tab) => (
            <button
              key={tab.id}
              className={clsx(
                'cursor-pointer w-[180px] h-[52px] text-[18px] font-bold flex items-center justify-center border-2 border-l-0 border-r-0 border-t-0 transition-colors',
                activeTab === tab.id
                  ? 'border-b-primary-100 text-primary-100'
                  : 'border-b-transparent text-secondary'
              )}
              onClick={() => {
                if (activeTab !== tab.id) {
                  setLoading(true);
                  setCurrentPage(1);
                  setSessions([]);
                  if (search) setSearch('');
                  setActiveTab(tab.id);
                }
              }}
            >
              {tab.name}
            </button>
          ))}
        </div>
        {deleteSessionIds?.length > 0 && (
          <Button
            onClick={() => {
              setIsDeleteOpen(true);
            }}
            variant="other"
            width="max-w-fit"
            className=" w-fit border border-[#FF0000]
              text-[#FF0000] bg-transparent hover:border-[#FF0000]"
            text={
              <div className="flex justify-center items-center gap-x-1 h-3 max-w-fit">
                <DeleteIcon
                  height={16}
                  width={16}
                  fill="#FF0000"
                  className="hover:scale-105 cursor-pointer"
                />
                Delete
              </div>
            }
          />
        )}
      </div>
      <div className="flex flex-1 h-full overflow-auto flex-col custom-scrollbar pr-2 my-2">
        {loading && !paginatedData?.length && (
          <div className="flex py-6  w-full justify-center items-center">
            <Loader fill="#fff" height={36} width={36} />
          </div>
        )}
        {!loading && !paginatedData?.length && currentPage === 1 && (
          <div className="flex py-6 text-secondary flex-1 h-full w-full justify-center items-center">
            No sessions recorded
          </div>
        )}

        {paginatedData.length > 0 && (
          <>
            {paginatedData.map(({ date, items }) => (
              <div
                // key={id}
                className="p-6 border border-[#F1F1F1] rounded-[10px] shadow-[0px_4px_14px_0px_#9E9E9E1A] my-6"
              >
                <div className="pb-8">
                  <text className="text-xl font-normal text-black">
                    {/* {getFormattedDate(date)} */}
                    {formatDate(date)}
                  </text>
                </div>
                <div className="flex flex-col gap-y-6">
                  {items.map((session) => (
                    <div
                      key={session?.id}
                      className="flex items-center justify-between "
                    // ref={index === 0 ? latestEntryRef : null}
                    >
                      {/* Left Section: Checkbox & Time */}
                      <div className="flex items-center gap-4 w-[7.44%] min-w-[111px]">
                        <CustomCheckbox
                          checked={deleteSessionIds.includes(session.id)}
                          onChange={() => handleSelectSession(session?.id)}
                        />

                        <div className="flex items-center gap-2">
                          <ClockIcon height={14} width={14} />
                          <span className="text-sm text-secondary">
                            {formatTime(session.updated_at)}
                          </span>
                        </div>
                      </div>

                      {/* Middle Section: Truncated Text */}
                      <div
                        className="w-[86.16%] pl-6 cursor-pointer hover:underline hover:underline-offset-4"
                        onClick={() => {
                          console.log("activeTab", activeTab)
                          console.log("Check", activeTab === 2)
                          activeTab === 2 ?
                            navigate(PATHS.QUICK_CALC, {
                              state: {
                                from: 'HISTORY',
                                sessionId: session.session_id,
                                tab: activeTab,
                                isDownloadAvailable: session?.download_available,
                                id: session?.id,
                              },
                            })
                            :
                            localStorage.setItem('NAVIGATED_FROM', 'HISTORY');
                          navigate(PATHS.HOME, {
                            state: {
                              from: 'HISTORY',
                              sessionId: session.session_id,
                              tab: activeTab,
                              isDownloadAvailable: session?.download_available,
                              id: session?.id,
                            },
                          });
                        }}
                      >
                        <div className="w-[95%] flex items-center overflow-hidden whitespace-nowrap text-ellipsis">
                          <span className="pl-1">
                            {trim(session.session_title, 85)}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
            {/* Infinite Scroll Trigger */}
            {currentPage <= totalPages && (
              <div ref={observerRef} className="text-center p-4">
                {loading ? <Loader height={36} width={36} fill="#fff" /> : ''}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default History;
